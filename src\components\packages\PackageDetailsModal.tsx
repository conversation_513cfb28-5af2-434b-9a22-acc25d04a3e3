'use client';

import React from 'react';
import { PackageData } from '@/types/packages';
import { Modal } from '@/components/ui';
import { PackageImage } from './PackageImage';
import {
  XMarkIcon,
  CheckCircleIcon,
  XCircleIcon,
  ClockIcon,
  CurrencyDollarIcon,
  TagIcon,
  DocumentTextIcon,
  SparklesIcon,
  InformationCircleIcon
} from '@heroicons/react/24/outline';

interface PackageDetailsModalProps {
  isOpen: boolean;
  onClose: () => void;
  package: PackageData | null;
}

export const PackageDetailsModal: React.FC<PackageDetailsModalProps> = ({
  isOpen,
  onClose,
  package: pkg
}) => {
  if (!pkg) return null;

  const hasImages = pkg.images && pkg.images.length > 0;

  return (
    <Modal isOpen={isOpen} onClose={onClose} size="large" title={pkg.name}>
      <div className="relative">
        {/* Header with Status Badge */}
        <div className="flex items-center justify-between px-6 py-4 bg-gray-50 border-b border-gray-200">
          <div className="flex items-center space-x-4">
            <div className="flex items-center space-x-2">
              <span className={`inline-flex items-center px-3 py-1.5 rounded-full text-sm font-medium shadow-sm transition-all duration-200 ${
                pkg.isActive
                  ? 'bg-green-100 text-green-800 border border-green-200'
                  : 'bg-red-100 text-red-800 border border-red-200'
              }`}>
                {pkg.isActive ? (
                  <>
                    <CheckCircleIcon className="h-4 w-4 mr-1.5" />
                    ✅ Active
                  </>
                ) : (
                  <>
                    <XCircleIcon className="h-4 w-4 mr-1.5" />
                    ❌ Inactive
                  </>
                )}
              </span>
              {pkg.isActive && (
                <span className="text-xs text-green-600 font-medium" title="This service is currently available">
                  Available for booking
                </span>
              )}
            </div>
          </div>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-all duration-200 p-2 rounded-full hover:bg-gray-200 focus:outline-none focus:ring-2 focus:ring-[var(--primary-green)] focus:ring-offset-2"
            title="Close"
            aria-label="Close modal"
          >
            <XMarkIcon className="h-5 w-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 md:p-8 max-h-[calc(90vh-160px)] overflow-y-auto">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Left Column - Images and Quick Info */}
            <div className="space-y-8">
              {/* Package Images */}
              <div className="bg-gray-50 rounded-xl overflow-hidden shadow-sm">
                {hasImages ? (
                  <PackageImage
                    images={pkg.images}
                    alt={pkg.name}
                    size="large"
                    className="w-full h-64 sm:h-72 object-cover"
                  />
                ) : (
                  <div className="w-full h-64 sm:h-72 flex flex-col items-center justify-center text-gray-400 bg-gradient-to-br from-gray-50 to-gray-100">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-16 w-16 mb-3 opacity-60" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={1} d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z" />
                    </svg>
                    <span className="text-sm font-medium">No image available</span>
                  </div>
                )}
              </div>

              {/* Quick Info Cards - Enhanced */}
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-5 rounded-xl border border-blue-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                  <div className="flex items-center mb-2">
                    <div className="p-2 bg-blue-200 rounded-lg mr-3">
                      <CurrencyDollarIcon className="h-5 w-5 text-blue-700" />
                    </div>
                    <span className="text-sm font-semibold text-blue-900 uppercase tracking-wide">Price</span>
                  </div>
                  <p className="text-2xl font-bold text-blue-900">₱{pkg.price.toLocaleString()}</p>
                </div>

                <div className="bg-gradient-to-br from-purple-50 to-purple-100 p-5 rounded-xl border border-purple-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                  <div className="flex items-center mb-2">
                    <div className="p-2 bg-purple-200 rounded-lg mr-3">
                      <ClockIcon className="h-5 w-5 text-purple-700" />
                    </div>
                    <span className="text-sm font-semibold text-purple-900 uppercase tracking-wide">Processing</span>
                  </div>
                  <p className="text-lg font-bold text-purple-900">{pkg.processingTime}</p>
                </div>
              </div>
            </div>

            {/* Right Column - Details */}
            <div className="space-y-8">
              {/* Description Section */}
              <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
                <div className="flex items-center mb-4">
                  <div className="p-2 bg-gray-100 rounded-lg mr-3">
                    <DocumentTextIcon className="h-5 w-5 text-gray-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900">Description</h3>
                </div>
                <div className="border-l-4 border-[var(--primary-green)] pl-4">
                  <p className="text-gray-800 leading-relaxed text-base font-medium">{pkg.description}</p>
                </div>
              </div>

              {/* Service Details Section */}
              <div className="bg-white border border-gray-200 rounded-xl p-6 shadow-sm">
                <div className="flex items-center mb-4">
                  <div className="p-2 bg-gray-100 rounded-lg mr-3">
                    <InformationCircleIcon className="h-5 w-5 text-gray-600" />
                  </div>
                  <h3 className="text-xl font-bold text-gray-900">Service Details</h3>
                </div>
                <div className="flex flex-wrap gap-3">
                  <span className="inline-flex items-center px-4 py-2 rounded-xl text-sm font-semibold bg-gradient-to-r from-blue-100 to-blue-200 text-blue-800 border border-blue-300 shadow-sm">
                    <TagIcon className="h-4 w-4 mr-2" />
                    {pkg.category}
                  </span>
                  <span className="inline-flex items-center px-4 py-2 rounded-xl text-sm font-semibold bg-gradient-to-r from-purple-100 to-purple-200 text-purple-800 border border-purple-300 shadow-sm">
                    <SparklesIcon className="h-4 w-4 mr-2" />
                    {pkg.cremationType}
                  </span>
                </div>
              </div>

              {/* What's Included Section */}
              {pkg.inclusions && pkg.inclusions.length > 0 && (
                <div className="bg-green-50 border border-green-200 rounded-xl p-6 shadow-sm">
                  <div className="flex items-center mb-4">
                    <div className="p-2 bg-green-100 rounded-lg mr-3">
                      <CheckCircleIcon className="h-5 w-5 text-green-600" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">What's Included</h3>
                  </div>
                  <div className="grid grid-cols-1 gap-3">
                    {pkg.inclusions.map((inclusion, idx) => (
                      <div key={idx} className="flex items-start bg-white p-3 rounded-lg border border-green-200 shadow-sm">
                        <div className="p-1 bg-green-100 rounded-full mr-3 mt-0.5">
                          <CheckCircleIcon className="h-4 w-4 text-green-600" />
                        </div>
                        <span className="text-gray-800 font-medium leading-relaxed">{inclusion}</span>
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Available Add-ons Section */}
              {pkg.addOns && pkg.addOns.length > 0 && (
                <div className="bg-amber-50 border border-amber-200 rounded-xl p-6 shadow-sm">
                  <div className="flex items-center mb-4">
                    <div className="p-2 bg-amber-100 rounded-lg mr-3">
                      <SparklesIcon className="h-5 w-5 text-amber-600" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">Available Add-ons</h3>
                  </div>
                  <div className="grid grid-cols-1 gap-3">
                    {pkg.addOns.map((addon, idx) => (
                      <div key={idx} className="flex items-center justify-between p-4 bg-white rounded-lg border border-amber-200 shadow-sm hover:shadow-md transition-shadow duration-200">
                        <div className="flex items-center">
                          <div className="p-1 bg-amber-100 rounded-full mr-3">
                            <span className="text-amber-600 text-sm">+</span>
                          </div>
                          <span className="text-gray-800 font-medium">
                            {typeof addon === 'string' ? addon : addon.name}
                          </span>
                        </div>
                        {typeof addon !== 'string' && addon.price && (
                          <span className="text-sm font-bold text-amber-700 bg-amber-100 px-3 py-1 rounded-full">
                            +₱{addon.price.toLocaleString()}
                          </span>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              )}

              {/* Terms & Conditions Section */}
              {pkg.conditions && (
                <div className="bg-gray-50 border border-gray-200 rounded-xl p-6 shadow-sm">
                  <div className="flex items-center mb-4">
                    <div className="p-2 bg-gray-100 rounded-lg mr-3">
                      <DocumentTextIcon className="h-5 w-5 text-gray-600" />
                    </div>
                    <h3 className="text-xl font-bold text-gray-900">Terms & Conditions</h3>
                  </div>
                  <div className="bg-white p-4 rounded-lg border border-gray-200">
                    <p className="text-gray-800 text-sm leading-relaxed font-medium">{pkg.conditions}</p>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </Modal>
  );
};
